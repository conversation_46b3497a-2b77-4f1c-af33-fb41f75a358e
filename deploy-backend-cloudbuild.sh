#!/bin/bash

# FaceTrace Backend API Deployment Script using Cloud Build
# This script uses Cloud Build to build and deploy, no local Docker required

set -e

# Configuration
PROJECT_ID="gold-braid-458901-v2"
SERVICE_NAME="facetrace-backend-api"
REGION="us-central1"

echo "🚀 Starting FaceTrace Backend API deployment using Cloud Build..."

# Step 1: Authenticate and set project
echo "📋 Step 1: Setting up Google Cloud authentication..."
echo "🔍 Current project: $(gcloud config get-value project 2>/dev/null || echo 'Not set')"
echo "🔍 Current account: $(gcloud config get-value account 2>/dev/null || echo 'Not logged in')"

# Check if already authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo "🔐 Please authenticate with Google Cloud..."
    gcloud auth login
else
    echo "✅ Already authenticated"
fi

gcloud config set project $PROJECT_ID
echo "✅ Project set to: $PROJECT_ID"

echo "🔧 Enabling required APIs..."
gcloud services enable run.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable containerregistry.googleapis.com

# Step 2: Create cloudbuild.yaml if it doesn't exist
echo "📝 Step 2: Creating Cloud Build configuration..."
cat > cloudbuild.yaml << 'EOF'
steps:
  # Build the container image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/facetrace-backend-api', '-f', 'Dockerfile.backend', '.']
  
  # Push the container image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/facetrace-backend-api']
  
  # Deploy container image to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
    - 'run'
    - 'deploy'
    - 'facetrace-backend-api'
    - '--image'
    - 'gcr.io/$PROJECT_ID/facetrace-backend-api'
    - '--region'
    - 'us-central1'
    - '--platform'
    - 'managed'
    - '--allow-unauthenticated'
    - '--memory'
    - '2Gi'
    - '--cpu'
    - '2'
    - '--timeout'
    - '3600'
    - '--concurrency'
    - '1000'
    - '--max-instances'
    - '10'
    - '--min-instances'
    - '0'
    - '--set-env-vars'
    - 'NODE_ENV=production,NEXT_PUBLIC_DISABLE_AUTH=true,NEXT_PUBLIC_DISABLE_PAYMENT=true'

images:
  - gcr.io/$PROJECT_ID/facetrace-backend-api
EOF

# Step 3: Submit build to Cloud Build
echo "☁️ Step 3: Building and deploying using Cloud Build..."
gcloud builds submit --config cloudbuild.yaml . || {
    echo "❌ Cloud Build deployment failed!"
    echo "📝 Please check the error messages above and try again"
    exit 1
}

echo "✅ Deployment completed!"
echo "🌐 Your backend API is available at:"
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region $REGION --format 'value(status.url)' 2>/dev/null || echo "Unable to get URL - check Cloud Run console")
echo $SERVICE_URL

echo ""
echo "📝 Next steps:"
echo "1. Run: chmod +x setup-env-vars.sh && ./setup-env-vars.sh"
echo "2. Run: chmod +x test-backend-api.sh && ./test-backend-api.sh"
echo "3. Configure your frontend to use: $SERVICE_URL"
echo "4. Set up custom domain (optional)"
echo ""
echo "🔧 Important: Update your frontend environment variables:"
echo "NEXT_PUBLIC_API_BASE_URL=$SERVICE_URL"

# Clean up
rm -f cloudbuild.yaml
echo "🧹 Cleaned up temporary files"
