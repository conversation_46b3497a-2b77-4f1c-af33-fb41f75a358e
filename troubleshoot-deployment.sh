#!/bin/bash

# FaceTrace Backend API Troubleshooting Script
# Helps diagnose common deployment issues

set -e

PROJECT_ID="gold-braid-458901-v2"
SERVICE_NAME="facetrace-backend-api"
REGION="us-central1"

echo "🔍 FaceTrace Backend API Troubleshooting..."
echo "Project: $PROJECT_ID"
echo "Service: $SERVICE_NAME"
echo "Region: $REGION"
echo ""

# Check authentication
echo "🔐 Checking authentication..."
CURRENT_ACCOUNT=$(gcloud config get-value account 2>/dev/null || echo "Not logged in")
CURRENT_PROJECT=$(gcloud config get-value project 2>/dev/null || echo "Not set")

echo "Current account: $CURRENT_ACCOUNT"
echo "Current project: $CURRENT_PROJECT"

if [ "$CURRENT_PROJECT" != "$PROJECT_ID" ]; then
    echo "⚠️  Warning: Current project ($CURRENT_PROJECT) doesn't match expected project ($PROJECT_ID)"
    echo "Run: gcloud config set project $PROJECT_ID"
fi

if [ "$CURRENT_ACCOUNT" = "Not logged in" ]; then
    echo "❌ Not authenticated. Run: gcloud auth login"
    exit 1
fi

echo "✅ Authentication OK"
echo ""

# Check required APIs
echo "🔧 Checking required APIs..."
REQUIRED_APIS=("run.googleapis.com" "cloudbuild.googleapis.com" "containerregistry.googleapis.com")

for api in "${REQUIRED_APIS[@]}"; do
    if gcloud services list --enabled --filter="name:$api" --format="value(name)" | grep -q "$api"; then
        echo "✅ $api is enabled"
    else
        echo "❌ $api is not enabled"
        echo "Run: gcloud services enable $api"
    fi
done

echo ""

# Check if service exists
echo "🔍 Checking Cloud Run service..."
if gcloud run services describe $SERVICE_NAME --region $REGION --project $PROJECT_ID &>/dev/null; then
    echo "✅ Service '$SERVICE_NAME' exists"
    
    # Get service details
    echo ""
    echo "📊 Service details:"
    gcloud run services describe $SERVICE_NAME --region $REGION --format="table(
        metadata.name,
        status.url,
        status.conditions[0].type,
        status.conditions[0].status,
        spec.template.spec.containers[0].image
    )"
    
    echo ""
    echo "🌐 Service URL:"
    gcloud run services describe $SERVICE_NAME --region $REGION --format='value(status.url)'
    
    echo ""
    echo "📋 Environment variables:"
    gcloud run services describe $SERVICE_NAME --region $REGION --format='value(spec.template.spec.containers[0].env[].name,spec.template.spec.containers[0].env[].value)' | paste - -
    
else
    echo "❌ Service '$SERVICE_NAME' not found"
    echo "📝 Available services in region $REGION:"
    gcloud run services list --region $REGION --format="table(metadata.name,status.url)"
fi

echo ""

# Check recent deployments
echo "📈 Checking recent Cloud Build history..."
gcloud builds list --limit=5 --format="table(id,status,createTime,source.repoSource.repoName)"

echo ""

# Check logs if service exists
if gcloud run services describe $SERVICE_NAME --region $REGION --project $PROJECT_ID &>/dev/null; then
    echo "📋 Recent logs (last 10 entries):"
    gcloud run services logs read $SERVICE_NAME --region $REGION --limit=10 --format="table(timestamp,severity,textPayload)" || echo "No logs available"
fi

echo ""
echo "🔧 Common solutions:"
echo "1. If service doesn't exist: Run ./deploy-backend-cloudbuild.sh"
echo "2. If APIs not enabled: Run gcloud services enable [api-name]"
echo "3. If authentication issues: Run gcloud auth login"
echo "4. If project issues: Run gcloud config set project $PROJECT_ID"
echo "5. If deployment fails: Check Cloud Build logs in the console"
echo ""
echo "🌐 Useful links:"
echo "- Cloud Run Console: https://console.cloud.google.com/run?project=$PROJECT_ID"
echo "- Cloud Build Console: https://console.cloud.google.com/cloud-build?project=$PROJECT_ID"
echo "- Logs Console: https://console.cloud.google.com/logs?project=$PROJECT_ID"
