# FaceTrace Backend Deployment - Cloud Build Ignore File
# This file specifies which files should be ignored when uploading to Cloud Build

# Include package-lock.json (override .gitignore)
!package-lock.json

# Exclude development and build artifacts
node_modules/
.next/
.git/
.env*
!.env.example

# Exclude test files
test/
coverage/
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# Exclude documentation and notes
*.md
!README.md
docs/
notes/

# Exclude IDE files
.vscode/
.idea/
*.sublime-*

# Exclude OS files
.DS_Store
Thumbs.db

# Exclude logs
*.log
logs/

# Exclude temporary files
*.tmp
*.temp
.cache/

# Exclude deployment scripts (we don't need them in the build)
deploy-*.sh
setup-*.sh
test-*.sh
troubleshoot-*.sh
*.yaml
!cloudbuild.yaml

# Exclude memory bank and other large directories
memory-bank/
