# FaceTrace Backend API Deployment Guide

This guide covers deploying the FaceTrace backend API to Google Cloud Run, focusing only on the API components needed for polling and data processing.

## 🏗️ Architecture Overview

- **Service**: Google Cloud Run (recommended for Next.js API routes)
- **Container**: Docker-based deployment with optimized Next.js build
- **Scaling**: Automatic scaling from 0 to 10 instances
- **Timeout**: 1 hour for long-running operations
- **Memory**: 2GB per instance
- **CPU**: 2 vCPU per instance

## 📋 Prerequisites

1. **Google Cloud CLI** installed and configured
2. **Docker** installed and running
3. **Google Cloud Project** (gold-braid-458901-v2) with billing enabled
4. **Required APIs** will be enabled automatically by the deployment script

## 🚀 Quick Deployment

### Step 1: Deploy the Backend API

```bash
# Make the script executable and run deployment
chmod +x deploy-backend.sh
./deploy-backend.sh
```

This script will:
- Authenticate with Google Cloud
- Enable required APIs
- Build and push Docker image
- Deploy to Cloud Run
- Configure basic environment variables

### Step 2: Configure Environment Variables

```bash
# Set up production environment variables
chmod +x setup-env-vars.sh
./setup-env-vars.sh
```

**Important**: You'll need to manually configure:
- Database connection string
- reCAPTCHA keys
- External API keys
- Any other sensitive configuration

### Step 3: Test the Deployment

```bash
# Test all API endpoints
chmod +x test-backend-api.sh
./test-backend-api.sh
```

## 🔧 Manual Configuration Steps

### Database Configuration

```bash
gcloud run services update facetrace-backend-api \
  --region us-central1 \
  --set-env-vars DATABASE_URL="your-database-connection-string"
```

### reCAPTCHA Configuration

```bash
gcloud run services update facetrace-backend-api \
  --region us-central1 \
  --set-env-vars NEXT_PUBLIC_RECAPTCHA_SITE_KEY="your-site-key" \
  --set-env-vars RECAPTCHA_SECRET_KEY="your-secret-key"
```

### External API Keys

```bash
gcloud run services update facetrace-backend-api \
  --region us-central1 \
  --set-env-vars SEARCH_API_KEY="your-search-api-key" \
  --set-env-vars SEARCH_API_URL="your-search-api-url"
```

## 📊 Monitoring and Logging

### Set up monitoring:

```bash
chmod +x setup-monitoring.sh
./setup-monitoring.sh
```

### Access monitoring dashboards:
- **Logs**: [Cloud Logging Console](https://console.cloud.google.com/logs)
- **Monitoring**: [Cloud Monitoring Console](https://console.cloud.google.com/monitoring)
- **Cloud Run**: [Cloud Run Console](https://console.cloud.google.com/run)

## 🌐 Custom Domain (Optional)

### Set up custom domain:

```bash
# Edit the domain configuration in the script first
chmod +x setup-custom-domain.sh
./setup-custom-domain.sh
```

## 🔗 API Endpoints

Once deployed, your backend API will be available at:
`https://facetrace-backend-api-[hash]-uc.a.run.app`

### Available endpoints:
- `GET /api/health` - Health check
- `GET /api/core?action=faces` - Get faces count
- `POST /api/search` - Search operations
- `POST /api/data` - Data operations

## 🔒 Security Considerations

1. **CORS Configuration**: Update the allowed origins in `next.config.backend.js`
2. **Environment Variables**: Use Google Secret Manager for sensitive data
3. **Authentication**: API is configured with auth disabled for polling
4. **Rate Limiting**: Consider implementing rate limiting for production

## 🔄 Frontend Integration

Update your frontend environment variables:

```env
NEXT_PUBLIC_API_BASE_URL=https://
```

## 📝 Troubleshooting

### Common Issues:

1. **Build Failures**: Check Docker build logs
2. **Environment Variables**: Verify all required variables are set
3. **Database Connections**: Ensure database is accessible from Cloud Run
4. **Memory Issues**: Increase memory allocation if needed

### Useful Commands:

```bash
# View logs
gcloud run services logs read facetrace-backend-api --region us-central1

# Update service
gcloud run services update facetrace-backend-api --region us-central1 [options]

# Get service details
gcloud run services describe facetrace-backend-api --region us-central1
```

## 💰 Cost Optimization

- **Auto-scaling**: Service scales to zero when not in use
- **Resource allocation**: Optimized for API workloads
- **Request-based billing**: Pay only for actual usage

## 🔄 Updates and Maintenance

To update the backend:

1. Make your code changes
2. Run `./deploy-backend.sh` again
3. Test with `./test-backend-api.sh`

The deployment script handles versioning automatically.
